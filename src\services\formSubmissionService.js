import { patientSessionService } from './patientSessionService';

/**
 * Form Submission Service
 * Handles saving different form types to the database
 */
class FormSubmissionService {
  
  /**
   * Submit Patient Demographic Sheet (Patient Intake Form)
   */
  async submitPatientDemographicSheet(formData) {
    try {
      // Transform form data to match database schema
      const dbData = {
        full_legal_name: formData.fullLegalName,
        date: formData.date,
        phone: formData.phone,
        email: formData.email,
        address: formData.address,
        city_state_zip: formData.cityStateZip,
        age: formData.age ? parseInt(formData.age) : null,
        date_of_birth: formData.dob,
        ssn: formData.ssn,
        gender: formData.gender,
        active_duty_service_member: formData.activeDutyServiceMember,
        dod_benefit: formData.dodBenefit,
        current_employer: formData.currentEmployer,
        spouse_name: formData.spouseName,
        spouse_age: formData.spouseAge ? parseInt(formData.spouseAge) : null,
        spouse_dob: formData.spouseDob,
        spouse_ssn: formData.spouseSsn,
        spouse_employer: formData.spouseEmployer,
        referring_provider: formData.referringProvider,
        primary_health_insurance: formData.primaryHealthInsurance,
        policy: formData.policy,
        group_number: formData.group,
        known_medical_conditions: formData.knownMedicalConditions,
        drug_allergies: formData.drugAllergies,
        current_medications: formData.currentMedications
      };

      // Save to database
      const result = await patientSessionService.saveFormData(
        'patient_demographic_sheets', 
        dbData
      );

      if (result.error) {
        throw result.error;
      }

      // Update patient info across all tables since this form contains name and email
      if (formData.fullLegalName && formData.email) {
        await patientSessionService.updatePatientInfo(
          formData.fullLegalName,
          formData.email
        );
      }

      return { success: true, data: result.data };
    } catch (error) {
      console.error('Error submitting patient demographic sheet:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Submit Medical History Form
   */
  async submitMedicalHistory(formData) {
    try {
      const dbData = {
        medical_conditions: formData.medicalConditions,
        suicidal_thoughts: formData.suicidalThoughts,
        attempts: formData.attempts,
        suicidal_explanation: formData.suicidalExplanation,
        previous_psychiatrist: formData.previousPsychiatrist,
        psychiatric_hospitalizations: formData.psychiatricHospitalizations,
        legal_charges: formData.legalCharges,
        legal_explanation: formData.legalExplanation,
        allergies: formData.allergies,
        signature: formData.signature
      };

      const result = await patientSessionService.saveFormData(
        'medical_history', 
        dbData
      );

      if (result.error) {
        throw result.error;
      }

      return { success: true, data: result.data };
    } catch (error) {
      console.error('Error submitting medical history:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Submit Pre-Cert Med List Form
   */
  async submitPreCertMedList(formData) {
    try {
      const dbData = {
        name: formData.name,
        date_of_birth: formData.dateOfBirth,
        selected_medications: formData.selectedMedications,
        medication_details: formData.medicationDetails
      };

      const result = await patientSessionService.saveFormData(
        'pre_cert_med_list', 
        dbData
      );

      if (result.error) {
        throw result.error;
      }

      return { success: true, data: result.data };
    } catch (error) {
      console.error('Error submitting pre-cert med list:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Submit BDI Form
   */
  async submitBDI(formData, totalScore) {
    try {
      const dbData = {
        responses: formData.responses,
        total_score: totalScore
      };

      const result = await patientSessionService.saveFormData(
        'bdi_responses', 
        dbData
      );

      if (result.error) {
        throw result.error;
      }

      return { success: true, data: result.data };
    } catch (error) {
      console.error('Error submitting BDI:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Submit PHQ-9 Form
   */
  async submitPHQ9(formData, totalScore) {
    try {
      const dbData = {
        responses: formData.responses,
        total_score: totalScore
      };

      const result = await patientSessionService.saveFormData(
        'phq9_responses', 
        dbData
      );

      if (result.error) {
        throw result.error;
      }

      return { success: true, data: result.data };
    } catch (error) {
      console.error('Error submitting PHQ-9:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Check if any forms have been submitted for current session
   */
  async getSubmissionStatus() {
    try {
      const sessionId = await patientSessionService.getCurrentSessionId();
      if (!sessionId) {
        return {
          hasSession: false,
          submissions: {}
        };
      }

      const forms = await patientSessionService.getSessionForms();
      
      return {
        hasSession: true,
        sessionId,
        submissions: {
          patientDemographic: !!forms?.patient_demographic_sheets,
          medicalHistory: !!forms?.medical_history,
          preCertMedList: !!forms?.pre_cert_med_list,
          bdi: !!forms?.bdi_responses,
          phq9: !!forms?.phq9_responses
        },
        formData: forms
      };
    } catch (error) {
      console.error('Error getting submission status:', error);
      return {
        hasSession: false,
        submissions: {},
        error: error.message
      };
    }
  }

  /**
   * Clear all session data (useful for testing or starting fresh)
   */
  async clearSessionData() {
    try {
      await patientSessionService.clearSession();
      return { success: true };
    } catch (error) {
      console.error('Error clearing session data:', error);
      return { success: false, error: error.message };
    }
  }
}

// Export singleton instance
export const formSubmissionService = new FormSubmissionService();
export default formSubmissionService;
