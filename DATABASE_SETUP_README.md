# TMS Application Database Setup

This document provides instructions for setting up the database to store patient form submissions in your TMS application.

## Overview

The application now supports storing all patient form data in a Supabase database without requiring user authentication. The system uses a session-based approach to link all forms submitted by the same patient.

## Database Schema

The following tables have been created to store form data:

1. **patient_sessions** - Tracks patient sessions and stores patient identification
2. **patient_demographic_sheets** - Patient intake form data
3. **medical_history** - Medical history form data
4. **pre_cert_med_list** - Pre-certification medication list data
5. **bdi_responses** - Beck Depression Inventory responses
6. **phq9_responses** - PHQ-9 questionnaire responses

## Setup Instructions

### 1. Run SQL Commands in Supabase

1. Open your Supabase project dashboard
2. Navigate to the SQL Editor
3. Copy and paste the entire content of `supabase_setup.sql` file
4. Execute the SQL commands

This will create:
- All necessary tables with proper schema
- Indexes for better performance
- Row Level Security policies for anonymous access
- Helper functions for updating patient information

### 2. Verify Environment Variables

Ensure your Expo app has the correct Supabase environment variables configured:

```javascript
// In your app.json or expo configuration
{
  "expo": {
    "extra": {
      "REACT_APP_SUPABASE_URL": "your-supabase-url",
      "REACT_APP_ANON_KEY": "your-supabase-anon-key"
    }
  }
}
```

## How It Works

### Session Management

1. **Session Creation**: When a patient first opens any form, a unique session ID is generated and stored locally
2. **Form Submission**: Each form submission is linked to the session ID
3. **Patient Identification**: When the Patient Intake Form (containing name and email) is submitted, all forms for that session are updated with the patient's information

### Form Flow

The patient can fill out forms in any order:

1. **Any Form First**: Patient can start with any form (BDI, PHQ-9, Medical History, etc.)
2. **Session Tracking**: All submissions are tracked under the same session
3. **Patient Intake Form**: When this form is submitted (contains name and email), all previous submissions are linked to the patient
4. **Data Integrity**: All forms for a patient are properly connected regardless of submission order

### Database Operations

Each form now:
- ✅ Validates required fields
- ✅ Shows loading state during submission
- ✅ Saves data to appropriate database table
- ✅ Handles errors gracefully
- ✅ Updates patient information across all tables when intake form is submitted

## Form-Specific Details

### Patient Demographic Sheet (Patient Intake Form)
- **Table**: `patient_demographic_sheets`
- **Special Function**: Updates patient name/email across all tables
- **Key Fields**: Full name, email, phone, address, demographics

### Medical History Form
- **Table**: `medical_history`
- **Key Fields**: Medical conditions (JSON), psychiatric history, legal history

### Pre-Cert Med List Form
- **Table**: `pre_cert_med_list`
- **Key Fields**: Selected medications (JSON), medication details (JSON)

### BDI Form
- **Table**: `bdi_responses`
- **Key Fields**: All 21 responses (JSON), calculated total score

### PHQ-9 Form
- **Table**: `phq9_responses`
- **Key Fields**: All 9 responses (JSON), calculated total score

## Data Structure Examples

### Session Data
```json
{
  "session_id": "session_1704067200_abc123",
  "patient_name": "John Doe",
  "patient_email": "<EMAIL>",
  "created_at": "2024-01-01T00:00:00Z"
}
```

### BDI Responses
```json
{
  "responses": {
    "0": "1",
    "1": "2",
    "2": "0",
    // ... all 21 responses
  },
  "total_score": 15
}
```

### Medical Conditions
```json
{
  "ASTHMA": true,
  "ANXIETY": true,
  "DIABETES": false,
  // ... all conditions
}
```

## Security Considerations

- **Anonymous Access**: Tables are configured for anonymous access since there's no authentication
- **Row Level Security**: Enabled on all tables with permissive policies
- **Data Validation**: Client-side validation ensures data integrity
- **Session Isolation**: Each session is isolated from others

## Monitoring and Maintenance

### Viewing Data
You can view submitted data in the Supabase dashboard:
1. Go to Table Editor
2. Select any table to view submissions
3. Use filters to find specific patients or sessions

### Common Queries
```sql
-- Get all forms for a specific patient
SELECT * FROM patient_sessions WHERE patient_email = '<EMAIL>';

-- Get submission statistics
SELECT 
  COUNT(*) as total_submissions,
  COUNT(DISTINCT session_id) as unique_patients
FROM patient_demographic_sheets;

-- Get incomplete sessions (no patient info)
SELECT * FROM patient_sessions WHERE patient_name IS NULL;
```

## Troubleshooting

### Common Issues

1. **Forms not submitting**: Check network connection and Supabase credentials
2. **Data not linking**: Ensure Patient Intake Form is submitted to link all forms
3. **Missing data**: Check that all required fields are filled before submission

### Error Handling

The application includes comprehensive error handling:
- Network errors are caught and displayed to users
- Validation errors prevent submission
- Loading states prevent duplicate submissions
- Graceful fallbacks for unexpected errors

## Testing

To test the implementation:

1. Fill out any form (e.g., BDI) and submit
2. Check Supabase tables - data should appear with session_id but no patient info
3. Fill out Patient Intake Form with name and email
4. Check tables again - all previous submissions should now have patient info
5. Submit additional forms - they should immediately include patient info

## Support

If you encounter issues:
1. Check the browser console for error messages
2. Verify Supabase connection in the Network tab
3. Ensure all SQL commands were executed successfully
4. Check that environment variables are correctly configured
