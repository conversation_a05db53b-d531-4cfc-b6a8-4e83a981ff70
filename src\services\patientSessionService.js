import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '@utils/supabase';

const SESSION_KEY = 'patient_session_id';

/**
 * Patient Session Service
 * Manages patient sessions for form submissions without authentication
 */
class PatientSessionService {
  constructor() {
    this.currentSessionId = null;
  }

  /**
   * Generate a unique session ID
   */
  generateSessionId() {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 10);
    return `session_${timestamp}_${random}`;
  }

  /**
   * Get or create a patient session
   */
  async getOrCreateSession() {
    try {
      // Check if we already have a session in memory
      if (this.currentSessionId) {
        return this.currentSessionId;
      }

      // Check AsyncStorage for existing session
      const storedSessionId = await AsyncStorage.getItem(SESSION_KEY);
      
      if (storedSessionId) {
        // Verify session exists in database
        const { data, error } = await supabase
          .from('patient_sessions')
          .select('session_id')
          .eq('session_id', storedSessionId)
          .single();

        if (!error && data) {
          this.currentSessionId = storedSessionId;
          return storedSessionId;
        }
      }

      // Create new session
      const newSessionId = this.generateSessionId();
      
      // Save to database
      const { error } = await supabase
        .from('patient_sessions')
        .insert({
          session_id: newSessionId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (error) {
        console.error('Error creating patient session:', error);
        throw error;
      }

      // Save to AsyncStorage
      await AsyncStorage.setItem(SESSION_KEY, newSessionId);
      this.currentSessionId = newSessionId;

      return newSessionId;
    } catch (error) {
      console.error('Error in getOrCreateSession:', error);
      throw error;
    }
  }

  /**
   * Update patient information in session and all related forms
   */
  async updatePatientInfo(patientName, patientEmail) {
    try {
      const sessionId = await this.getOrCreateSession();

      // Call the database function to update patient info across all tables
      const { error } = await supabase.rpc('update_patient_info_across_tables', {
        p_session_id: sessionId,
        p_patient_name: patientName,
        p_patient_email: patientEmail
      });

      if (error) {
        console.error('Error updating patient info:', error);
        throw error;
      }

      return { success: true };
    } catch (error) {
      console.error('Error in updatePatientInfo:', error);
      throw error;
    }
  }

  /**
   * Clear current session (useful for testing or starting fresh)
   */
  async clearSession() {
    try {
      await AsyncStorage.removeItem(SESSION_KEY);
      this.currentSessionId = null;
      return { success: true };
    } catch (error) {
      console.error('Error clearing session:', error);
      throw error;
    }
  }

  /**
   * Get current session ID without creating a new one
   */
  async getCurrentSessionId() {
    if (this.currentSessionId) {
      return this.currentSessionId;
    }

    const storedSessionId = await AsyncStorage.getItem(SESSION_KEY);
    if (storedSessionId) {
      this.currentSessionId = storedSessionId;
    }

    return this.currentSessionId;
  }

  /**
   * Save form data to database
   */
  async saveFormData(tableName, formData, additionalData = {}) {
    try {
      const sessionId = await this.getOrCreateSession();

      const dataToSave = {
        session_id: sessionId,
        ...formData,
        ...additionalData,
        submitted_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from(tableName)
        .insert(dataToSave)
        .select();

      if (error) {
        console.error(`Error saving ${tableName} data:`, error);
        throw error;
      }

      return { data: data[0], error: null };
    } catch (error) {
      console.error(`Error in saveFormData for ${tableName}:`, error);
      return { data: null, error };
    }
  }

  /**
   * Update existing form data
   */
  async updateFormData(tableName, formData, additionalData = {}) {
    try {
      const sessionId = await this.getOrCreateSession();

      const dataToUpdate = {
        ...formData,
        ...additionalData,
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from(tableName)
        .update(dataToUpdate)
        .eq('session_id', sessionId)
        .select();

      if (error) {
        console.error(`Error updating ${tableName} data:`, error);
        throw error;
      }

      return { data: data[0], error: null };
    } catch (error) {
      console.error(`Error in updateFormData for ${tableName}:`, error);
      return { data: null, error };
    }
  }

  /**
   * Check if a form has already been submitted for this session
   */
  async hasFormBeenSubmitted(tableName) {
    try {
      const sessionId = await this.getCurrentSessionId();
      if (!sessionId) return false;

      const { data, error } = await supabase
        .from(tableName)
        .select('id')
        .eq('session_id', sessionId)
        .single();

      return !error && data;
    } catch (error) {
      console.error(`Error checking if ${tableName} has been submitted:`, error);
      return false;
    }
  }

  /**
   * Get all form submissions for current session
   */
  async getSessionForms() {
    try {
      const sessionId = await this.getCurrentSessionId();
      if (!sessionId) return null;

      const tables = [
        'patient_demographic_sheets',
        'medical_history',
        'pre_cert_med_list',
        'bdi_responses',
        'phq9_responses'
      ];

      const results = {};

      for (const table of tables) {
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .eq('session_id', sessionId);

        if (!error && data && data.length > 0) {
          results[table] = data[0];
        }
      }

      return results;
    } catch (error) {
      console.error('Error getting session forms:', error);
      return null;
    }
  }
}

// Export singleton instance
export const patientSessionService = new PatientSessionService();
export default patientSessionService;
