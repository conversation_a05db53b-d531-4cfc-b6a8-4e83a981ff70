-- TMS Application Database Schema
-- Run these commands in your Supabase SQL Editor

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 1. Patient Sessions Table (for tracking form submissions without authentication)
CREATE TABLE IF NOT EXISTS patient_sessions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    session_id TEXT UNIQUE NOT NULL,
    patient_name TEXT,
    patient_email TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Patient Demographic Sheets Table
CREATE TABLE IF NOT EXISTS patient_demographic_sheets (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    session_id TEXT NOT NULL,
    patient_name TEXT,
    patient_email TEXT,
    full_legal_name TEXT NOT NULL,
    date DATE,
    phone TEXT,
    email TEXT,
    address TEXT,
    city_state_zip TEXT,
    age INTEGER,
    date_of_birth DATE,
    ssn TEXT,
    gender TEXT,
    active_duty_service_member TEXT,
    dod_benefit TEXT,
    current_employer TEXT,
    spouse_name TEXT,
    spouse_age INTEGER,
    spouse_dob DATE,
    spouse_ssn TEXT,
    spouse_employer TEXT,
    referring_provider TEXT,
    primary_health_insurance TEXT,
    policy TEXT,
    group_number TEXT,
    known_medical_conditions TEXT,
    drug_allergies TEXT,
    current_medications TEXT,
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Medical History Table
CREATE TABLE IF NOT EXISTS medical_history (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    session_id TEXT NOT NULL,
    patient_name TEXT,
    patient_email TEXT,
    medical_conditions JSONB, -- Store as JSON object
    suicidal_thoughts TEXT,
    attempts TEXT,
    suicidal_explanation TEXT,
    previous_psychiatrist TEXT,
    psychiatric_hospitalizations TEXT,
    legal_charges TEXT,
    legal_explanation TEXT,
    allergies TEXT,
    signature TEXT,
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Pre-Cert Med List Table
CREATE TABLE IF NOT EXISTS pre_cert_med_list (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    session_id TEXT NOT NULL,
    patient_name TEXT,
    patient_email TEXT,
    name TEXT NOT NULL,
    date_of_birth DATE,
    selected_medications JSONB, -- Store as JSON object
    medication_details JSONB, -- Store as JSON object
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. BDI (Beck Depression Inventory) Table
CREATE TABLE IF NOT EXISTS bdi_responses (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    session_id TEXT NOT NULL,
    patient_name TEXT,
    patient_email TEXT,
    responses JSONB NOT NULL, -- Store all 21 responses as JSON
    total_score INTEGER,
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. PHQ-9 (Patient Health Questionnaire) Table
CREATE TABLE IF NOT EXISTS phq9_responses (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    session_id TEXT NOT NULL,
    patient_name TEXT,
    patient_email TEXT,
    responses JSONB NOT NULL, -- Store all 9 responses as JSON
    total_score INTEGER,
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_patient_sessions_session_id ON patient_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_patient_demographic_sheets_session_id ON patient_demographic_sheets(session_id);
CREATE INDEX IF NOT EXISTS idx_medical_history_session_id ON medical_history(session_id);
CREATE INDEX IF NOT EXISTS idx_pre_cert_med_list_session_id ON pre_cert_med_list(session_id);
CREATE INDEX IF NOT EXISTS idx_bdi_responses_session_id ON bdi_responses(session_id);
CREATE INDEX IF NOT EXISTS idx_phq9_responses_session_id ON phq9_responses(session_id);

-- Create indexes for patient identification
CREATE INDEX IF NOT EXISTS idx_patient_sessions_email ON patient_sessions(patient_email);
CREATE INDEX IF NOT EXISTS idx_patient_demographic_sheets_email ON patient_demographic_sheets(patient_email);
CREATE INDEX IF NOT EXISTS idx_medical_history_email ON medical_history(patient_email);
CREATE INDEX IF NOT EXISTS idx_pre_cert_med_list_email ON pre_cert_med_list(patient_email);
CREATE INDEX IF NOT EXISTS idx_bdi_responses_email ON bdi_responses(patient_email);
CREATE INDEX IF NOT EXISTS idx_phq9_responses_email ON phq9_responses(patient_email);

-- Enable Row Level Security (RLS) for all tables
ALTER TABLE patient_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE patient_demographic_sheets ENABLE ROW LEVEL SECURITY;
ALTER TABLE medical_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE pre_cert_med_list ENABLE ROW LEVEL SECURITY;
ALTER TABLE bdi_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE phq9_responses ENABLE ROW LEVEL SECURITY;

-- Create policies to allow anonymous access (since no authentication)
-- Note: In production, you might want to restrict this further
CREATE POLICY "Allow anonymous access to patient_sessions" ON patient_sessions
    FOR ALL USING (true);

CREATE POLICY "Allow anonymous access to patient_demographic_sheets" ON patient_demographic_sheets
    FOR ALL USING (true);

CREATE POLICY "Allow anonymous access to medical_history" ON medical_history
    FOR ALL USING (true);

CREATE POLICY "Allow anonymous access to pre_cert_med_list" ON pre_cert_med_list
    FOR ALL USING (true);

CREATE POLICY "Allow anonymous access to bdi_responses" ON bdi_responses
    FOR ALL USING (true);

CREATE POLICY "Allow anonymous access to phq9_responses" ON phq9_responses
    FOR ALL USING (true);

-- Create a function to update patient information across all tables when intake form is submitted
CREATE OR REPLACE FUNCTION update_patient_info_across_tables(
    p_session_id TEXT,
    p_patient_name TEXT,
    p_patient_email TEXT
)
RETURNS void AS $$
BEGIN
    -- Update patient_sessions
    UPDATE patient_sessions
    SET patient_name = p_patient_name,
        patient_email = p_patient_email,
        updated_at = NOW()
    WHERE session_id = p_session_id;

    -- Update all form tables with patient info
    UPDATE patient_demographic_sheets
    SET patient_name = p_patient_name,
        patient_email = p_patient_email,
        updated_at = NOW()
    WHERE session_id = p_session_id;

    UPDATE medical_history
    SET patient_name = p_patient_name,
        patient_email = p_patient_email,
        updated_at = NOW()
    WHERE session_id = p_session_id;

    UPDATE pre_cert_med_list
    SET patient_name = p_patient_name,
        patient_email = p_patient_email,
        updated_at = NOW()
    WHERE session_id = p_session_id;

    UPDATE bdi_responses
    SET patient_name = p_patient_name,
        patient_email = p_patient_email,
        updated_at = NOW()
    WHERE session_id = p_session_id;

    UPDATE phq9_responses
    SET patient_name = p_patient_name,
        patient_email = p_patient_email,
        updated_at = NOW()
    WHERE session_id = p_session_id;
END;
$$ LANGUAGE plpgsql;

-- Create a function to generate unique session IDs
CREATE OR REPLACE FUNCTION generate_session_id()
RETURNS TEXT AS $$
BEGIN
    RETURN 'session_' || extract(epoch from now())::bigint || '_' || substr(md5(random()::text), 1, 8);
END;
$$ LANGUAGE plpgsql;
