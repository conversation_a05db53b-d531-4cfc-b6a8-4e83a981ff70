-- TMS Application Database Setup for Supabase
-- Run these commands in the Supabase SQL Editor

-- Enable Row Level Security (RLS) for all tables
-- This ensures data security and proper access control

-- 1. Patient Demographic Sheets Table
CREATE TABLE IF NOT EXISTS patient_demographic_sheets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    full_legal_name TEXT NOT NULL,
    date DATE,
    phone TEXT,
    email TEXT,
    address TEXT,
    city_state_zip TEXT,
    age INTEGER,
    date_of_birth DATE,
    ssn TEXT,
    gender TEXT,
    active_duty_service_member TEXT,
    dod_benefit TEXT,
    current_employer TEXT,
    spouse_name TEXT,
    spouse_age INTEGER,
    spouse_dob DATE,
    spouse_ssn TEXT,
    spouse_employer TEXT,
    referring_provider TEXT,
    primary_health_insurance TEXT,
    policy TEXT,
    group_number TEXT,
    known_medical_conditions TEXT,
    drug_allergies TEXT,
    current_medications TEXT,
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. PHQ-9 Submissions Table
CREATE TABLE IF NOT EXISTS phq9_submissions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    responses JSONB NOT NULL,
    total_score INTEGER NOT NULL DEFAULT 0,
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. BDI Submissions Table
CREATE TABLE IF NOT EXISTS bdi_submissions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    responses JSONB NOT NULL,
    total_score INTEGER NOT NULL DEFAULT 0,
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Medical History Submissions Table
CREATE TABLE IF NOT EXISTS medical_history_submissions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    medical_conditions JSONB,
    suicidal_thoughts TEXT,
    attempts TEXT,
    suicidal_explanation TEXT,
    previous_psychiatrist TEXT,
    psychiatric_hospitalizations TEXT,
    legal_charges TEXT,
    legal_explanation TEXT,
    allergies TEXT,
    signature TEXT,
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Pre-Certification Medication List Submissions Table
CREATE TABLE IF NOT EXISTS pre_cert_med_list_submissions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    date_of_birth DATE,
    selected_medications JSONB,
    medication_details JSONB,
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_patient_demographic_sheets_submitted_at ON patient_demographic_sheets(submitted_at);
CREATE INDEX IF NOT EXISTS idx_patient_demographic_sheets_email ON patient_demographic_sheets(email);
CREATE INDEX IF NOT EXISTS idx_phq9_submissions_submitted_at ON phq9_submissions(submitted_at);
CREATE INDEX IF NOT EXISTS idx_phq9_submissions_total_score ON phq9_submissions(total_score);
CREATE INDEX IF NOT EXISTS idx_bdi_submissions_submitted_at ON bdi_submissions(submitted_at);
CREATE INDEX IF NOT EXISTS idx_bdi_submissions_total_score ON bdi_submissions(total_score);
CREATE INDEX IF NOT EXISTS idx_medical_history_submissions_submitted_at ON medical_history_submissions(submitted_at);
CREATE INDEX IF NOT EXISTS idx_pre_cert_med_list_submissions_submitted_at ON pre_cert_med_list_submissions(submitted_at);
CREATE INDEX IF NOT EXISTS idx_pre_cert_med_list_submissions_name ON pre_cert_med_list_submissions(name);

-- Enable Row Level Security (RLS)
ALTER TABLE patient_demographic_sheets ENABLE ROW LEVEL SECURITY;
ALTER TABLE phq9_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE bdi_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE medical_history_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE pre_cert_med_list_submissions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for public access (since this is a patient intake system)
-- Note: In production, you might want to implement more restrictive policies

-- Patient Demographic Sheets policies
CREATE POLICY "Allow public insert on patient_demographic_sheets" ON patient_demographic_sheets
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow public select on patient_demographic_sheets" ON patient_demographic_sheets
    FOR SELECT USING (true);

-- PHQ-9 Submissions policies
CREATE POLICY "Allow public insert on phq9_submissions" ON phq9_submissions
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow public select on phq9_submissions" ON phq9_submissions
    FOR SELECT USING (true);

-- BDI Submissions policies
CREATE POLICY "Allow public insert on bdi_submissions" ON bdi_submissions
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow public select on bdi_submissions" ON bdi_submissions
    FOR SELECT USING (true);

-- Medical History Submissions policies
CREATE POLICY "Allow public insert on medical_history_submissions" ON medical_history_submissions
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow public select on medical_history_submissions" ON medical_history_submissions
    FOR SELECT USING (true);

-- Pre-Cert Med List Submissions policies
CREATE POLICY "Allow public insert on pre_cert_med_list_submissions" ON pre_cert_med_list_submissions
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow public select on pre_cert_med_list_submissions" ON pre_cert_med_list_submissions
    FOR SELECT USING (true);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update the updated_at column
CREATE TRIGGER update_patient_demographic_sheets_updated_at BEFORE UPDATE ON patient_demographic_sheets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_phq9_submissions_updated_at BEFORE UPDATE ON phq9_submissions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bdi_submissions_updated_at BEFORE UPDATE ON bdi_submissions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_medical_history_submissions_updated_at BEFORE UPDATE ON medical_history_submissions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pre_cert_med_list_submissions_updated_at BEFORE UPDATE ON pre_cert_med_list_submissions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create a view for form submission statistics (optional)
CREATE OR REPLACE VIEW form_submission_stats AS
SELECT 
    'Patient Demographic Sheets' as form_type,
    COUNT(*) as total_submissions,
    COUNT(*) FILTER (WHERE submitted_at >= CURRENT_DATE) as today_submissions,
    COUNT(*) FILTER (WHERE submitted_at >= CURRENT_DATE - INTERVAL '7 days') as week_submissions
FROM patient_demographic_sheets
UNION ALL
SELECT 
    'PHQ-9 Submissions' as form_type,
    COUNT(*) as total_submissions,
    COUNT(*) FILTER (WHERE submitted_at >= CURRENT_DATE) as today_submissions,
    COUNT(*) FILTER (WHERE submitted_at >= CURRENT_DATE - INTERVAL '7 days') as week_submissions
FROM phq9_submissions
UNION ALL
SELECT 
    'BDI Submissions' as form_type,
    COUNT(*) as total_submissions,
    COUNT(*) FILTER (WHERE submitted_at >= CURRENT_DATE) as today_submissions,
    COUNT(*) FILTER (WHERE submitted_at >= CURRENT_DATE - INTERVAL '7 days') as week_submissions
FROM bdi_submissions
UNION ALL
SELECT 
    'Medical History Submissions' as form_type,
    COUNT(*) as total_submissions,
    COUNT(*) FILTER (WHERE submitted_at >= CURRENT_DATE) as today_submissions,
    COUNT(*) FILTER (WHERE submitted_at >= CURRENT_DATE - INTERVAL '7 days') as week_submissions
FROM medical_history_submissions
UNION ALL
SELECT 
    'Pre-Cert Med List Submissions' as form_type,
    COUNT(*) as total_submissions,
    COUNT(*) FILTER (WHERE submitted_at >= CURRENT_DATE) as today_submissions,
    COUNT(*) FILTER (WHERE submitted_at >= CURRENT_DATE - INTERVAL '7 days') as week_submissions
FROM pre_cert_med_list_submissions;

-- Grant necessary permissions for the view
GRANT SELECT ON form_submission_stats TO anon, authenticated;

-- Insert some sample data for testing (optional - remove in production)
-- You can uncomment these lines to test the database structure

/*
INSERT INTO patient_demographic_sheets (
    full_legal_name, email, phone, address, city_state_zip, age, gender, active_duty_service_member
) VALUES (
    'Test Patient', '<EMAIL>', '555-0123', '123 Test St', 'Test City, TS 12345', 30, 'M', 'N'
);

INSERT INTO phq9_submissions (responses, total_score) VALUES (
    '{"0": "1", "1": "2", "2": "0", "3": "1", "4": "0", "5": "1", "6": "0", "7": "0", "8": "0"}', 5
);

INSERT INTO bdi_submissions (responses, total_score) VALUES (
    '{"0": "1", "1": "0", "2": "2", "3": "1"}', 4
);
*/

-- Additional useful queries and views for monitoring and analytics

-- Create a view for PHQ-9 score analysis
CREATE OR REPLACE VIEW phq9_score_analysis AS
SELECT
    total_score,
    CASE
        WHEN total_score BETWEEN 0 AND 4 THEN 'Minimal'
        WHEN total_score BETWEEN 5 AND 9 THEN 'Mild'
        WHEN total_score BETWEEN 10 AND 14 THEN 'Moderate'
        WHEN total_score BETWEEN 15 AND 19 THEN 'Moderately Severe'
        WHEN total_score >= 20 THEN 'Severe'
        ELSE 'Unknown'
    END as severity_level,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
FROM phq9_submissions
GROUP BY total_score
ORDER BY total_score;

-- Create a view for BDI score analysis
CREATE OR REPLACE VIEW bdi_score_analysis AS
SELECT
    total_score,
    CASE
        WHEN total_score BETWEEN 0 AND 13 THEN 'Minimal'
        WHEN total_score BETWEEN 14 AND 19 THEN 'Mild'
        WHEN total_score BETWEEN 20 AND 28 THEN 'Moderate'
        WHEN total_score >= 29 THEN 'Severe'
        ELSE 'Unknown'
    END as severity_level,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
FROM bdi_submissions
GROUP BY total_score
ORDER BY total_score;

-- Create a view for daily submission trends
CREATE OR REPLACE VIEW daily_submission_trends AS
SELECT
    DATE(submitted_at) as submission_date,
    COUNT(*) FILTER (WHERE submitted_at IN (SELECT submitted_at FROM patient_demographic_sheets)) as demographic_sheets,
    COUNT(*) FILTER (WHERE submitted_at IN (SELECT submitted_at FROM phq9_submissions)) as phq9_forms,
    COUNT(*) FILTER (WHERE submitted_at IN (SELECT submitted_at FROM bdi_submissions)) as bdi_forms,
    COUNT(*) FILTER (WHERE submitted_at IN (SELECT submitted_at FROM medical_history_submissions)) as medical_history_forms,
    COUNT(*) FILTER (WHERE submitted_at IN (SELECT submitted_at FROM pre_cert_med_list_submissions)) as med_list_forms
FROM (
    SELECT submitted_at FROM patient_demographic_sheets
    UNION ALL
    SELECT submitted_at FROM phq9_submissions
    UNION ALL
    SELECT submitted_at FROM bdi_submissions
    UNION ALL
    SELECT submitted_at FROM medical_history_submissions
    UNION ALL
    SELECT submitted_at FROM pre_cert_med_list_submissions
) all_submissions
GROUP BY DATE(submitted_at)
ORDER BY submission_date DESC;

-- Create a function to get submission summary for a date range
CREATE OR REPLACE FUNCTION get_submission_summary(start_date DATE, end_date DATE)
RETURNS TABLE (
    form_type TEXT,
    submission_count BIGINT,
    avg_per_day NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        'Patient Demographic Sheets'::TEXT,
        COUNT(*),
        ROUND(COUNT(*)::NUMERIC / (end_date - start_date + 1), 2)
    FROM patient_demographic_sheets
    WHERE DATE(submitted_at) BETWEEN start_date AND end_date

    UNION ALL

    SELECT
        'PHQ-9 Submissions'::TEXT,
        COUNT(*),
        ROUND(COUNT(*)::NUMERIC / (end_date - start_date + 1), 2)
    FROM phq9_submissions
    WHERE DATE(submitted_at) BETWEEN start_date AND end_date

    UNION ALL

    SELECT
        'BDI Submissions'::TEXT,
        COUNT(*),
        ROUND(COUNT(*)::NUMERIC / (end_date - start_date + 1), 2)
    FROM bdi_submissions
    WHERE DATE(submitted_at) BETWEEN start_date AND end_date

    UNION ALL

    SELECT
        'Medical History Submissions'::TEXT,
        COUNT(*),
        ROUND(COUNT(*)::NUMERIC / (end_date - start_date + 1), 2)
    FROM medical_history_submissions
    WHERE DATE(submitted_at) BETWEEN start_date AND end_date

    UNION ALL

    SELECT
        'Pre-Cert Med List Submissions'::TEXT,
        COUNT(*),
        ROUND(COUNT(*)::NUMERIC / (end_date - start_date + 1), 2)
    FROM pre_cert_med_list_submissions
    WHERE DATE(submitted_at) BETWEEN start_date AND end_date;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions for new views and functions
GRANT SELECT ON phq9_score_analysis TO anon, authenticated;
GRANT SELECT ON bdi_score_analysis TO anon, authenticated;
GRANT SELECT ON daily_submission_trends TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_submission_summary(DATE, DATE) TO anon, authenticated;

-- Example queries for monitoring (commented out - uncomment to use)

-- Get submissions for the last 7 days
-- SELECT * FROM get_submission_summary(CURRENT_DATE - INTERVAL '7 days', CURRENT_DATE);

-- Get PHQ-9 severity distribution
-- SELECT severity_level, count, percentage FROM phq9_score_analysis;

-- Get BDI severity distribution
-- SELECT severity_level, count, percentage FROM bdi_score_analysis;

-- Get recent daily trends
-- SELECT * FROM daily_submission_trends LIMIT 30;

-- Find patients with high depression scores (PHQ-9 >= 15)
-- SELECT id, total_score, submitted_at FROM phq9_submissions WHERE total_score >= 15 ORDER BY submitted_at DESC;

-- Find patients with high BDI scores (>= 20)
-- SELECT id, total_score, submitted_at FROM bdi_submissions WHERE total_score >= 20 ORDER BY submitted_at DESC;

-- Get medical conditions frequency
-- SELECT condition, COUNT(*) as frequency
-- FROM medical_history_submissions,
--      jsonb_each_text(medical_conditions) as condition_data(condition, value)
-- WHERE condition_data.value = 'true'
-- GROUP BY condition
-- ORDER BY frequency DESC;

-- Final message
SELECT 'TMS Application database setup completed successfully with analytics!' as message;
